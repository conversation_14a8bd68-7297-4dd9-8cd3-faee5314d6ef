---
name: Bug report
about: Create a report to help us improve
title: 'bug: '
labels: bug
assignees: ''
---

### Description

Detailed description of the problem, including steps to reproduce the issue.

### Steps to Reproduce

1. Step-by-step instructions to reproduce the issue
2. Include command examples or UI interactions

### Expected Behavior

Describe clearly what the expected outcome or behavior should be.

### Actual Behavior

Describe clearly what the actual outcome or behavior is.

### Screenshots or Logs

Provide screenshots, logs, or error messages if applicable.

### Environment

- Task Master version:
- Node.js version:
- Operating system:
- IDE (if applicable):

### Additional Context

Any additional information or context that might help diagnose the issue.
