# Task ID: 31
# Title: Add Config Flag Support to task-master init Command
# Status: done
# Dependencies: None
# Priority: low
# Description: Enhance the 'task-master init' command to accept configuration flags that allow users to bypass the interactive CLI questions and directly provide configuration values.
# Details:
Currently, the 'task-master init' command prompts users with a series of questions to set up the configuration. This task involves modifying the init command to accept command-line flags that can pre-populate these configuration values, allowing for a non-interactive setup process.

Implementation steps:
1. Identify all configuration options that are currently collected through CLI prompts during initialization
2. Create corresponding command-line flags for each configuration option (e.g., --project-name, --ai-provider, etc.)
3. Modify the init command handler to check for these flags before starting the interactive prompts
4. If a flag is provided, skip the corresponding prompt and use the provided value instead
5. If all required configuration values are provided via flags, skip the interactive process entirely
6. Update the command's help text to document all available flags and their usage
7. Ensure backward compatibility so the command still works with the interactive approach when no flags are provided
8. Consider adding a --non-interactive flag that will fail if any required configuration is missing rather than prompting for it (useful for scripts and CI/CD)

The implementation should follow the existing command structure and use the same configuration file format. Make sure to validate flag values with the same validation logic used for interactive inputs.

# Test Strategy:
Testing should verify both the interactive and non-interactive paths work correctly:

1. Unit tests:
   - Test each flag individually to ensure it correctly overrides the corresponding prompt
   - Test combinations of flags to ensure they work together properly
   - Test validation of flag values to ensure invalid values are rejected
   - Test the --non-interactive flag to ensure it fails when required values are missing

2. Integration tests:
   - Test a complete initialization with all flags provided
   - Test partial initialization with some flags and some interactive prompts
   - Test initialization with no flags (fully interactive)

3. Manual testing scenarios:
   - Run 'task-master init --project-name="Test Project" --ai-provider="openai"' and verify it skips those prompts
   - Run 'task-master init --help' and verify all flags are documented
   - Run 'task-master init --non-interactive' without required flags and verify it fails with a helpful error message
   - Run a complete non-interactive initialization and verify the resulting configuration file matches expectations

Ensure the command's documentation is updated to reflect the new functionality, and verify that the help text accurately describes all available options.
