# Task ID: 83
# Title: Update config-manager.js defaults and getters
# Status: pending
# Dependencies: 82
# Priority: high
# Description: Modify the config-manager.js module to replace maxTokens with maxInputTokens and maxOutputTokens in the DEFAULTS object and update related getter functions.
# Details:
1. Update the `DEFAULTS` object in config-manager.js:
```javascript
const DEFAULTS = {
  // ... existing defaults
  main: {
    // Replace maxTokens with these two fields
    maxInputTokens: 16000, // Example default
    maxOutputTokens: 4000, // Example default
    temperature: 0.7
    // ... other fields
  },
  research: {
    maxInputTokens: 16000,
    maxOutputTokens: 4000,
    temperature: 0.7
    // ... other fields
  },
  fallback: {
    maxInputTokens: 8000,
    maxOutputTokens: 2000,
    temperature: 0.7
    // ... other fields
  }
  // ... rest of DEFAULTS
};
```

2. Update `getParametersForRole` function to return the new fields:
```javascript
function getParametersForRole(role, explicitRoot = null) {
  const config = _getConfig(explicitRoot);
  return {
    maxInputTokens: config[role]?.maxInputTokens,
    maxOutputTokens: config[role]?.maxOutputTokens,
    temperature: config[role]?.temperature
    // ... any other parameters
  };
}
```

3. Add a new function to get model capabilities:
```javascript
function getModelCapabilities(providerName, modelId) {
  const models = MODEL_MAP[providerName?.toLowerCase()];
  const model = models?.find(m => m.id === modelId);
  return {
    contextWindowTokens: model?.contextWindowTokens,
    maxOutputTokens: model?.maxOutputTokens
  };
}
```

4. Deprecate or update the role-specific maxTokens getters:
```javascript
// Either remove these or update them to return maxInputTokens
function getMainMaxTokens(explicitRoot = null) {
  console.warn('getMainMaxTokens is deprecated. Use getParametersForRole("main") instead.');
  return getParametersForRole("main", explicitRoot).maxInputTokens;
}
// Same for getResearchMaxTokens and getFallbackMaxTokens
```

5. Export the new functions:
```javascript
module.exports = {
  // ... existing exports
  getParametersForRole,
  getModelCapabilities
};
```

# Test Strategy:
1. Unit test the updated getParametersForRole function with various configurations
2. Verify the new getModelCapabilities function returns correct values
3. Test with both default and custom configurations
4. Ensure backward compatibility by checking that existing code using the old getters still works (with warnings)

# Subtasks:
## 1. Update config-manager.js with specific token limit fields [pending]
### Dependencies: None
### Description: Modify the DEFAULTS object in config-manager.js to replace maxTokens with more specific token limit fields (maxInputTokens, maxOutputTokens, maxTotalTokens) and update related getter functions while maintaining backward compatibility.
### Details:
1. Replace maxTokens in the DEFAULTS object with maxInputTokens, maxOutputTokens, and maxTotalTokens
2. Update any getter functions that reference maxTokens to handle both old and new configurations
3. Ensure backward compatibility so existing code using maxTokens continues to work
4. Update any related documentation or comments to reflect the new token limit fields
5. Test the changes to verify both new specific token limits and legacy maxTokens usage work correctly

